#!/usr/bin/env python3

import importlib.util

if importlib.util.find_spec('pyotp') is None:
    raise Exception("Required package requests not found, execute 'python3 -m pip install pytotp' before proceeding")

import pyotp
import base64

class TOTP:

    def generate_secret(self):
        sec = ''
        for i in range(8):
            sec = f"{sec}{pyotp.random_base32()}"
        return sec
    
    def generate_public(self, secret):
        if not isinstance(secret, bytes):
            secret = bytes(secret, 'ascii')
        secret = base64.b32encode(secret).decode('ascii').replace('=','')
        return secret

    def verify(self, secret, pin):
        value = False
        if not isinstance(secret, bytes):
            secret = bytes(secret, 'ascii')

        key = base64.b32encode(secret).decode('ascii')
        totp = pyotp.TOTP(key,interval = 180)
        
        if pin == None:
            pin = input("PIN: ")
        
        if totp.verify(pin, valid_window=3):
            value = True
        
        return value
    
    def get_otp(self, totp_string):
        totp = pyotp.TOTP(totp_string,interval = 180)
        otp = totp.now()
        return str(otp)
    