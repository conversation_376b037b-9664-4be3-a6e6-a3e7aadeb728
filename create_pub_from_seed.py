#!/usr/bin/env python3

from totp import *
import time
from tqdm import tqdm
otp = TOTP()

seed = input('Input Seed: ')
token = otp.generate_public(seed)
print('')
print('Public Token:')
print('-')
print(token)
print("-")
pin = otp.get_otp(token)
print(f'OTP Used: {pin}')
test = otp.verify(seed,pin)
print(f'PIN Pass 1: {test}')
for i in tqdm(range(30), bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt}", desc ="sleeping"):
    time.sleep(1)
test = otp.verify(seed,pin)
print(f'PIN Pass 2: {test}')
