#!/usr/bin/env python3
"""
TOTP Token Generator and Validator

This script generates a public TOTP token from a seed and tests its validation
over time to demonstrate the time-based nature of OTP codes.
"""

import time
import sys
from tqdm import tqdm
from totp import TOTP


def main():
    """Main function to generate and test TOTP tokens."""
    try:
        # Initialize TOTP instance
        otp = TOTP()

        # Get seed input from user
        seed = input('Input Seed: ').strip()
        if not seed:
            print("Error: Seed cannot be empty")
            sys.exit(1)

        # Generate public token from seed
        print("\nGenerating public token...")
        token = otp.generate_public(seed)

        # Display the generated token
        print('\n' + '='*50)
        print('Public Token:')
        print('-' * 20)
        print(token)
        print('-' * 20)

        # Generate current OTP from the token
        pin = otp.get_otp(token)
        print(f'\nCurrent OTP: {pin}')

        # Test initial verification
        print("\nTesting initial OTP verification...")
        test_result = otp.verify(seed, pin)
        print(f'Initial verification: {"PASS" if test_result else "FAIL"}')

        # Wait 30 seconds to test time-based expiration
        print("\nWaiting 30 seconds to test OTP expiration...")
        for _ in tqdm(range(30),
                     bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt}s",
                     desc="Waiting"):
            time.sleep(1)

        # Test verification after time delay
        print("\nTesting OTP verification after 30 seconds...")
        test_result = otp.verify(seed, pin)
        print(f'Delayed verification: {"PASS" if test_result else "FAIL"}')

        # Explain results
        print("\n" + "="*50)
        print("EXPLANATION:")
        print("- Initial verification should PASS (OTP is current)")
        print("- Delayed verification should FAIL (OTP has expired)")
        print("- This demonstrates the time-based nature of TOTP")
        print("="*50)

    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\nError: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
