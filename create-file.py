#!/usr/bin/env python3

from totp import *
import datetime

otp = TOTP()

seed = otp.generate_secret()
token = otp.generate_public(seed)

with open('required-files/script.txt', 'r') as f:
    script_content = f.read()

ticket_num = input('Ticket Number: ')

new_script = script_content.replace('<seed>',seed)

current_date = datetime.date.today()
date_file = current_date.strftime("%Y%m%d")
filename = f'otp_{str(ticket_num)}_{date_file}.py'
with open(filename, 'w') as f:
    f.write(new_script)
    
token = token.replace('otpauth://totp/AFS-CS-Support:<EMAIL>?algorithm=SHA1&digits=6&issuer=AFS-CS-Support&period=90&secret=','')
print(' OTP Token Value: ', token)
